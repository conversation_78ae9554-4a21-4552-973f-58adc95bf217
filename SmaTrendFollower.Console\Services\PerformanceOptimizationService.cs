using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced service for monitoring and optimizing system performance
/// Includes automatic bottleneck detection and optimization recommendations
/// </summary>
public interface IPerformanceOptimizationService : IDisposable
{
    // Legacy methods (maintained for compatibility)
    void StartOperation(string operationName);
    void EndOperation(string operationName);
    void RecordMetric(string metricName, double value, string? unit = null);
    Task<PerformanceReport> GenerateReportAsync();
    void LogPerformanceWarning(string operation, TimeSpan duration, TimeSpan threshold);

    // Enhanced methods
    Task<OptimizationResult> OptimizePerformanceAsync(CancellationToken cancellationToken = default);
    Task<OptimizationResult> ApplyOptimizationAsync(BottleneckType bottleneckType, string details, CancellationToken cancellationToken = default);
    Task<List<OptimizationRecommendation>> GetRecommendationsAsync();
    void SetAutomaticOptimization(bool enabled);
}

public sealed class PerformanceOptimizationService : IPerformanceOptimizationService
{
    private readonly ILogger<PerformanceOptimizationService> _logger;
    private readonly ConcurrentDictionary<string, Stopwatch> _activeOperations = new();
    private readonly ConcurrentDictionary<string, List<OptimizationMetric>> _metrics = new();
    private readonly ConcurrentQueue<OptimizationAction> _appliedOptimizations = new();
    private readonly Timer _optimizationTimer;
    private readonly object _lockObject = new();
    private bool _automaticOptimizationEnabled = false;
    private bool _disposed = false;

    public PerformanceOptimizationService(ILogger<PerformanceOptimizationService> logger)
    {
        _logger = logger;

        // Start optimization timer (runs every 5 minutes)
        _optimizationTimer = new Timer(PerformAutomaticOptimization, null,
            TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
    }

    public void StartOperation(string operationName)
    {
        var stopwatch = Stopwatch.StartNew();
        _activeOperations.AddOrUpdate(operationName, stopwatch, (key, existing) =>
        {
            existing.Stop();
            return stopwatch;
        });
        
        _logger.LogDebug("Started performance tracking for operation: {Operation}", operationName);
    }

    public void EndOperation(string operationName)
    {
        if (_activeOperations.TryRemove(operationName, out var stopwatch))
        {
            stopwatch.Stop();
            var duration = stopwatch.Elapsed;
            
            RecordMetric($"{operationName}_Duration", duration.TotalMilliseconds, "ms");
            
            // Log performance warnings for slow operations
            var thresholds = GetPerformanceThresholds();
            if (thresholds.TryGetValue(operationName, out var threshold) && duration > threshold)
            {
                LogPerformanceWarning(operationName, duration, threshold);
            }
            
            _logger.LogDebug("Completed operation {Operation} in {Duration}ms", 
                operationName, duration.TotalMilliseconds);
        }
        else
        {
            _logger.LogWarning("Attempted to end operation {Operation} that was not started", operationName);
        }
    }

    public void RecordMetric(string metricName, double value, string? unit = null)
    {
        var metric = new OptimizationMetric
        {
            Name = metricName,
            Value = value,
            Unit = unit ?? "count",
            Timestamp = DateTime.UtcNow
        };

        _metrics.AddOrUpdate(metricName,
            new List<OptimizationMetric> { metric },
            (key, existing) =>
            {
                lock (_lockObject)
                {
                    existing.Add(metric);
                    // Keep only last 1000 metrics per type to prevent memory bloat
                    if (existing.Count > 1000)
                    {
                        existing.RemoveRange(0, existing.Count - 1000);
                    }
                    return existing;
                }
            });
    }

    public async Task<PerformanceReport> GenerateReportAsync()
    {
        await Task.Yield(); // Make it async for future database operations
        
        var report = new PerformanceReport
        {
            GeneratedAt = DateTime.UtcNow,
            ActiveOperations = _activeOperations.Keys.ToList(),
            MetricSummaries = new Dictionary<string, MetricSummary>()
        };

        foreach (var kvp in _metrics)
        {
            var metricName = kvp.Key;
            var values = kvp.Value.ToList(); // Thread-safe copy
            
            if (values.Any())
            {
                var numericValues = values.Select(m => m.Value).ToList();
                var summary = new MetricSummary
                {
                    Name = metricName,
                    Count = values.Count,
                    Average = numericValues.Average(),
                    Min = numericValues.Min(),
                    Max = numericValues.Max(),
                    Unit = values.First().Unit,
                    LastRecorded = values.Max(v => v.Timestamp)
                };
                
                // Calculate percentiles
                var sortedValues = numericValues.OrderBy(v => v).ToList();
                summary.P50 = GetPercentile(sortedValues, 0.5);
                summary.P95 = GetPercentile(sortedValues, 0.95);
                summary.P99 = GetPercentile(sortedValues, 0.99);
                
                report.MetricSummaries[metricName] = summary;
            }
        }

        return report;
    }

    public void LogPerformanceWarning(string operation, TimeSpan duration, TimeSpan threshold)
    {
        _logger.LogWarning("Performance warning: {Operation} took {Duration}ms (threshold: {Threshold}ms)",
            operation, duration.TotalMilliseconds, threshold.TotalMilliseconds);
        
        RecordMetric($"{operation}_SlowOperations", 1);
    }

    private static Dictionary<string, TimeSpan> GetPerformanceThresholds()
    {
        return new Dictionary<string, TimeSpan>
        {
            ["UniverseBuilding"] = TimeSpan.FromSeconds(30),
            ["SignalGeneration"] = TimeSpan.FromSeconds(10),
            ["DatabaseQuery"] = TimeSpan.FromMilliseconds(500),
            ["ApiCall"] = TimeSpan.FromSeconds(5),
            ["CacheOperation"] = TimeSpan.FromMilliseconds(100),
            ["RiskCalculation"] = TimeSpan.FromMilliseconds(100),
            ["PortfolioAnalysis"] = TimeSpan.FromSeconds(2)
        };
    }

    private static double GetPercentile(List<double> sortedValues, double percentile)
    {
        if (!sortedValues.Any()) return 0;
        
        var index = percentile * (sortedValues.Count - 1);
        var lower = (int)Math.Floor(index);
        var upper = (int)Math.Ceiling(index);
        
        if (lower == upper)
        {
            return sortedValues[lower];
        }
        
        var weight = index - lower;
        return sortedValues[lower] * (1 - weight) + sortedValues[upper] * weight;
    }

    // Enhanced optimization methods
    public async Task<OptimizationResult> OptimizePerformanceAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting performance optimization analysis");

        var startTime = DateTime.UtcNow;
        var optimizations = new List<OptimizationAction>();

        try
        {
            // Analyze current metrics for bottlenecks
            var report = await GenerateReportAsync();

            // Check for slow operations
            foreach (var metric in report.MetricSummaries)
            {
                if (metric.Key.EndsWith("_Duration") && metric.Value.P95 > 5000) // 5 seconds
                {
                    optimizations.Add(new OptimizationAction
                    {
                        Type = "OPTIMIZE_SLOW_OPERATION",
                        Description = $"Optimize slow operation: {metric.Key} (P95: {metric.Value.P95:F1}ms)",
                        Priority = "High",
                        EstimatedImpact = "30-50% latency reduction"
                    });
                }
            }

            // Apply optimizations (simplified for now)
            var appliedCount = optimizations.Count;
            foreach (var optimization in optimizations)
            {
                _appliedOptimizations.Enqueue(optimization);
                optimization.AppliedAt = DateTime.UtcNow;
            }

            return new OptimizationResult
            {
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Success = true,
                TotalOptimizations = optimizations.Count,
                AppliedOptimizations = appliedCount,
                OptimizationActions = optimizations,
                Summary = $"Applied {appliedCount} optimizations"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Performance optimization failed");
            return new OptimizationResult
            {
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<OptimizationResult> ApplyOptimizationAsync(BottleneckType bottleneckType, string details, CancellationToken cancellationToken = default)
    {
        var optimization = CreateOptimizationAction(bottleneckType, details);
        _appliedOptimizations.Enqueue(optimization);
        optimization.AppliedAt = DateTime.UtcNow;

        return new OptimizationResult
        {
            StartTime = DateTime.UtcNow,
            EndTime = DateTime.UtcNow,
            Success = true,
            TotalOptimizations = 1,
            AppliedOptimizations = 1,
            OptimizationActions = new List<OptimizationAction> { optimization },
            Summary = $"Applied {optimization.Type} optimization"
        };
    }

    public async Task<List<OptimizationRecommendation>> GetRecommendationsAsync()
    {
        var recommendations = new List<OptimizationRecommendation>();
        var report = await GenerateReportAsync();

        // Analyze metrics for recommendations
        foreach (var metric in report.MetricSummaries)
        {
            if (metric.Key.EndsWith("_Duration") && metric.Value.P95 > 5000)
            {
                recommendations.Add(new OptimizationRecommendation
                {
                    Type = "HIGH_LATENCY_OPTIMIZATION",
                    Priority = "High",
                    Description = $"Operation {metric.Key} has high latency (P95: {metric.Value.P95:F1}ms)",
                    EstimatedImpact = "30-50% latency reduction",
                    Implementation = "Optimize algorithms, add caching, or implement async patterns"
                });
            }
        }

        return recommendations;
    }

    public void SetAutomaticOptimization(bool enabled)
    {
        _automaticOptimizationEnabled = enabled;
        _logger.LogInformation("Automatic optimization {Status}", enabled ? "enabled" : "disabled");
    }

    private OptimizationAction CreateOptimizationAction(BottleneckType bottleneckType, string details)
    {
        return new OptimizationAction
        {
            Type = bottleneckType.ToString(),
            Description = details,
            Priority = "Medium",
            EstimatedImpact = "Performance improvement expected"
        };
    }

    private async void PerformAutomaticOptimization(object? state)
    {
        if (!_automaticOptimizationEnabled) return;

        try
        {
            var result = await OptimizePerformanceAsync();
            if (result.AppliedOptimizations > 0)
            {
                _logger.LogInformation("Automatic optimization applied {Count} optimizations", result.AppliedOptimizations);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Automatic optimization failed");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _optimizationTimer?.Dispose();
            _disposed = true;
        }
    }
}

public class OptimizationMetric
{
    public string Name { get; set; } = string.Empty;
    public double Value { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
}

public class MetricSummary
{
    public string Name { get; set; } = string.Empty;
    public int Count { get; set; }
    public double Average { get; set; }
    public double Min { get; set; }
    public double Max { get; set; }
    public double P50 { get; set; }
    public double P95 { get; set; }
    public double P99 { get; set; }
    public string Unit { get; set; } = string.Empty;
    public DateTime LastRecorded { get; set; }
}

public class PerformanceReport
{
    public DateTime GeneratedAt { get; set; }
    public List<string> ActiveOperations { get; set; } = new();
    public Dictionary<string, MetricSummary> MetricSummaries { get; set; } = new();
}

// Enhanced optimization data models
public enum BottleneckType
{
    HighCpuUsage,
    HighMemoryUsage,
    SlowOperation,
    MemoryLeak,
    DatabaseBottleneck,
    NetworkBottleneck,
    ThreadContention
}

public class OptimizationAction
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string EstimatedImpact { get; set; } = string.Empty;
    public Dictionary<string, object>? Parameters { get; set; }
    public DateTime? AppliedAt { get; set; }
}

public class OptimizationResult
{
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool Success { get; set; }
    public int TotalOptimizations { get; set; }
    public int AppliedOptimizations { get; set; }
    public int FailedOptimizations { get; set; }
    public List<OptimizationAction> OptimizationActions { get; set; } = new();
    public string? Summary { get; set; }
    public string? ErrorMessage { get; set; }
}

public class OptimizationRecommendation
{
    public string Type { get; set; } = string.Empty;
    public string Priority { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string EstimatedImpact { get; set; } = string.Empty;
    public string Implementation { get; set; } = string.Empty;
}

using Microsoft.AspNetCore.Mvc;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Controllers;

/// <summary>
/// API controller for performance monitoring and optimization
/// Provides endpoints for real-time performance data, alerts, and optimization controls
/// </summary>
[ApiController]
[Route("api/[controller]")]
public class PerformanceMonitoringController : ControllerBase
{
    private readonly IRealTimePerformanceDashboard? _dashboard;
    private readonly IPerformanceAlertingService? _alertingService;
    private readonly ITradingSystemLoadTester? _loadTester;
    private readonly IPerformanceOptimizationService? _optimizationService;
    private readonly IEnhancedSystemResourceMonitor? _systemMonitor;
    private readonly IDatabasePerformanceMonitor? _databaseMonitor;
    private readonly IWebSocketPerformanceMonitor? _webSocketMonitor;
    private readonly ITradingPipelinePerformanceMonitor? _pipelineMonitor;
    private readonly IApiRateLimitMonitor? _apiRateLimitMonitor;

    public PerformanceMonitoringController(
        IRealTimePerformanceDashboard? dashboard = null,
        IPerformanceAlertingService? alertingService = null,
        ITradingSystemLoadTester? loadTester = null,
        IPerformanceOptimizationService? optimizationService = null,
        IEnhancedSystemResourceMonitor? systemMonitor = null,
        IDatabasePerformanceMonitor? databaseMonitor = null,
        IWebSocketPerformanceMonitor? webSocketMonitor = null,
        ITradingPipelinePerformanceMonitor? pipelineMonitor = null,
        IApiRateLimitMonitor? apiRateLimitMonitor = null)
    {
        _dashboard = dashboard;
        _alertingService = alertingService;
        _loadTester = loadTester;
        _optimizationService = optimizationService;
        _systemMonitor = systemMonitor;
        _databaseMonitor = databaseMonitor;
        _webSocketMonitor = webSocketMonitor;
        _pipelineMonitor = pipelineMonitor;
        _apiRateLimitMonitor = apiRateLimitMonitor;
    }

    /// <summary>
    /// Gets the current performance dashboard data
    /// </summary>
    [HttpGet("dashboard")]
    public async Task<ActionResult<DashboardData>> GetDashboard()
    {
        if (_dashboard == null)
            return NotFound("Performance dashboard service not available");

        var data = await _dashboard.GetDashboardDataAsync();
        return Ok(data);
    }

    /// <summary>
    /// Gets current system resource metrics
    /// </summary>
    [HttpGet("system-metrics")]
    public async Task<ActionResult<SystemResourceMetrics>> GetSystemMetrics()
    {
        if (_systemMonitor == null)
            return NotFound("System resource monitor not available");

        var metrics = await _systemMonitor.GetCurrentMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Gets database performance metrics
    /// </summary>
    [HttpGet("database-metrics")]
    public async Task<ActionResult<DatabasePerformanceMetrics>> GetDatabaseMetrics()
    {
        if (_databaseMonitor == null)
            return NotFound("Database performance monitor not available");

        var metrics = await _databaseMonitor.GetPerformanceMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Gets WebSocket performance metrics
    /// </summary>
    [HttpGet("websocket-metrics")]
    public async Task<ActionResult<WebSocketPerformanceMetrics>> GetWebSocketMetrics()
    {
        if (_webSocketMonitor == null)
            return NotFound("WebSocket performance monitor not available");

        var metrics = await _webSocketMonitor.GetPerformanceMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Gets trading pipeline performance metrics
    /// </summary>
    [HttpGet("pipeline-metrics")]
    public async Task<ActionResult<TradingPipelineMetrics>> GetPipelineMetrics()
    {
        if (_pipelineMonitor == null)
            return NotFound("Trading pipeline monitor not available");

        var metrics = await _pipelineMonitor.GetPipelineMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Gets active performance alerts
    /// </summary>
    [HttpGet("alerts")]
    public async Task<ActionResult<List<PerformanceAlert>>> GetActiveAlerts()
    {
        if (_alertingService == null)
            return NotFound("Performance alerting service not available");

        var alerts = await _alertingService.GetActiveAlertsAsync();
        return Ok(alerts);
    }

    /// <summary>
    /// Gets alert statistics
    /// </summary>
    [HttpGet("alerts/statistics")]
    public async Task<ActionResult<AlertStatistics>> GetAlertStatistics()
    {
        if (_alertingService == null)
            return NotFound("Performance alerting service not available");

        var stats = await _alertingService.GetAlertStatisticsAsync();
        return Ok(stats);
    }

    /// <summary>
    /// Gets bottleneck analysis
    /// </summary>
    [HttpGet("bottlenecks")]
    public async Task<ActionResult<BottleneckSummary>> GetBottleneckSummary()
    {
        if (_dashboard == null)
            return NotFound("Performance dashboard service not available");

        var summary = await _dashboard.GetBottleneckSummaryAsync();
        return Ok(summary);
    }

    /// <summary>
    /// Gets detailed bottleneck analysis
    /// </summary>
    [HttpGet("bottlenecks/analysis")]
    public async Task<ActionResult<BottleneckAnalysisResult>> GetBottleneckAnalysis()
    {
        if (_pipelineMonitor == null)
            return NotFound("Trading pipeline monitor not available");

        var analysis = await _pipelineMonitor.AnalyzeBottlenecksAsync();
        return Ok(analysis);
    }

    /// <summary>
    /// Gets optimization recommendations
    /// </summary>
    [HttpGet("optimization/recommendations")]
    public async Task<ActionResult<List<OptimizationRecommendation>>> GetOptimizationRecommendations()
    {
        if (_optimizationService == null)
            return NotFound("Performance optimization service not available");

        var recommendations = await _optimizationService.GetRecommendationsAsync();
        return Ok(recommendations);
    }

    /// <summary>
    /// Triggers performance optimization
    /// </summary>
    [HttpPost("optimization/optimize")]
    public async Task<ActionResult<OptimizationResult>> OptimizePerformance()
    {
        if (_optimizationService == null)
            return NotFound("Performance optimization service not available");

        var result = await _optimizationService.OptimizePerformanceAsync();
        return Ok(result);
    }

    /// <summary>
    /// Enables or disables automatic optimization
    /// </summary>
    [HttpPost("optimization/auto/{enabled}")]
    public ActionResult SetAutomaticOptimization(bool enabled)
    {
        if (_optimizationService == null)
            return NotFound("Performance optimization service not available");

        _optimizationService.SetAutomaticOptimization(enabled);
        return Ok(new { AutomaticOptimization = enabled });
    }

    /// <summary>
    /// Runs a comprehensive load test
    /// </summary>
    [HttpPost("load-test")]
    public async Task<ActionResult<LoadTestResult>> RunLoadTest([FromBody] LoadTestConfiguration config)
    {
        if (_loadTester == null)
            return NotFound("Load testing service not available");

        var result = await _loadTester.RunLoadTestAsync(config);
        return Ok(result);
    }

    /// <summary>
    /// Runs a signal generation load test
    /// </summary>
    [HttpPost("load-test/signals")]
    public async Task<ActionResult<LoadTestResult>> RunSignalLoadTest(
        [FromQuery] int concurrentSignals = 10,
        [FromQuery] int durationMinutes = 5)
    {
        if (_loadTester == null)
            return NotFound("Load testing service not available");

        var result = await _loadTester.RunSignalGenerationLoadTestAsync(concurrentSignals, durationMinutes);
        return Ok(result);
    }

    /// <summary>
    /// Runs a WebSocket stress test
    /// </summary>
    [HttpPost("load-test/websockets")]
    public async Task<ActionResult<LoadTestResult>> RunWebSocketStressTest(
        [FromQuery] int connectionCount = 5,
        [FromQuery] int messagesPerSecond = 100,
        [FromQuery] int durationMinutes = 5)
    {
        if (_loadTester == null)
            return NotFound("Load testing service not available");

        var result = await _loadTester.RunWebSocketStressTestAsync(connectionCount, messagesPerSecond, durationMinutes);
        return Ok(result);
    }

    /// <summary>
    /// Runs a database load test
    /// </summary>
    [HttpPost("load-test/database")]
    public async Task<ActionResult<LoadTestResult>> RunDatabaseLoadTest(
        [FromQuery] int concurrentOperations = 20,
        [FromQuery] int operationsPerSecond = 50,
        [FromQuery] int durationMinutes = 5)
    {
        if (_loadTester == null)
            return NotFound("Load testing service not available");

        var result = await _loadTester.RunDatabaseLoadTestAsync(concurrentOperations, operationsPerSecond, durationMinutes);
        return Ok(result);
    }

    /// <summary>
    /// Gets system health status
    /// </summary>
    [HttpGet("health")]
    public async Task<ActionResult<object>> GetHealthStatus()
    {
        var health = new
        {
            Timestamp = DateTime.UtcNow,
            Services = new
            {
                Dashboard = _dashboard != null,
                Alerting = _alertingService != null,
                LoadTesting = _loadTester != null,
                Optimization = _optimizationService != null,
                SystemMonitor = _systemMonitor != null,
                DatabaseMonitor = _databaseMonitor != null,
                WebSocketMonitor = _webSocketMonitor != null,
                PipelineMonitor = _pipelineMonitor != null
            },
            OverallHealth = _dashboard != null ? (await _dashboard.GetDashboardDataAsync()).OverallHealthScore : 0
        };

        return Ok(health);
    }

    /// <summary>
    /// Triggers a manual performance alert
    /// </summary>
    [HttpPost("alerts/trigger")]
    public async Task<ActionResult> TriggerAlert([FromBody] PerformanceAlert alert)
    {
        if (_alertingService == null)
            return NotFound("Performance alerting service not available");

        await _alertingService.TriggerAlertAsync(alert);
        return Ok(new { Message = "Alert triggered successfully" });
    }

    /// <summary>
    /// Gets API rate limit status for all services
    /// </summary>
    [HttpGet("api-rate-limits")]
    public async Task<ActionResult<ApiRateLimitStatus>> GetApiRateLimitStatus()
    {
        if (_apiRateLimitMonitor == null)
            return NotFound("API rate limit monitor not available");

        var status = await _apiRateLimitMonitor.GetRateLimitStatusAsync();
        return Ok(status);
    }

    /// <summary>
    /// Gets API performance metrics
    /// </summary>
    [HttpGet("api-performance")]
    public async Task<ActionResult<ApiPerformanceMetrics>> GetApiPerformanceMetrics()
    {
        if (_apiRateLimitMonitor == null)
            return NotFound("API rate limit monitor not available");

        var metrics = await _apiRateLimitMonitor.GetApiPerformanceMetricsAsync();
        return Ok(metrics);
    }

    /// <summary>
    /// Gets rate limit analysis and predictions
    /// </summary>
    [HttpGet("api-rate-limits/analysis")]
    public async Task<ActionResult<RateLimitAnalysis>> GetRateLimitAnalysis()
    {
        if (_apiRateLimitMonitor == null)
            return NotFound("API rate limit monitor not available");

        var analysis = await _apiRateLimitMonitor.AnalyzeRateLimitsAsync();
        return Ok(analysis);
    }

    /// <summary>
    /// Records a manual API request for testing
    /// </summary>
    [HttpPost("api-rate-limits/record")]
    public ActionResult RecordApiRequest([FromBody] ApiRequestRecord request)
    {
        if (_apiRateLimitMonitor == null)
            return NotFound("API rate limit monitor not available");

        _apiRateLimitMonitor.RecordApiRequest(
            request.Service,
            request.Endpoint,
            request.Method,
            request.StatusCode,
            request.Duration,
            request.RateLimitRemaining,
            request.RateLimitReset);

        return Ok(new { Message = "API request recorded successfully" });
    }
}

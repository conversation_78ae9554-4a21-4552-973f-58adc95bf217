using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using System.Diagnostics;
using System.Runtime.InteropServices;

namespace SmaTrendFollower.Services;

/// <summary>
/// Enhanced system resource monitoring service for real-time trading loads
/// Monitors CPU, memory, disk I/O, network, and other critical system resources
/// </summary>
public interface IEnhancedSystemResourceMonitor : IDisposable
{
    /// <summary>
    /// Event fired when resource thresholds are exceeded
    /// </summary>
    event EventHandler<ResourceAlertEventArgs>? ResourceAlertTriggered;
    
    /// <summary>
    /// Gets current system resource metrics
    /// </summary>
    Task<SystemResourceMetrics> GetCurrentMetricsAsync();
    
    /// <summary>
    /// Starts continuous monitoring
    /// </summary>
    Task StartMonitoringAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stops monitoring
    /// </summary>
    Task StopMonitoringAsync();
}

/// <summary>
/// Enhanced system resource monitoring service implementation
/// </summary>
public sealed class EnhancedSystemResourceMonitor : BackgroundService, IEnhancedSystemResourceMonitor
{
    private readonly ILogger<EnhancedSystemResourceMonitor> _logger;
    private readonly SystemResourceThresholds _thresholds;
    private readonly PerformanceCounter? _cpuCounter;
    private readonly PerformanceCounter? _memoryCounter;
    private readonly PerformanceCounter? _diskReadCounter;
    private readonly PerformanceCounter? _diskWriteCounter;
    private readonly PerformanceCounter? _networkSentCounter;
    private readonly PerformanceCounter? _networkReceivedCounter;
    private readonly Process _currentProcess;
    private readonly Timer _metricsTimer;
    private bool _disposed;

    public event EventHandler<ResourceAlertEventArgs>? ResourceAlertTriggered;

    public EnhancedSystemResourceMonitor(ILogger<EnhancedSystemResourceMonitor> logger)
    {
        _logger = logger;
        _thresholds = new SystemResourceThresholds();
        _currentProcess = Process.GetCurrentProcess();
        
        // Initialize performance counters (Windows only)
        if (OperatingSystem.IsWindows())
        {
            try
            {
                _cpuCounter = new PerformanceCounter("Processor", "% Processor Time", "_Total");
                _memoryCounter = new PerformanceCounter("Memory", "Available MBytes");
                _diskReadCounter = new PerformanceCounter("PhysicalDisk", "Disk Reads/sec", "_Total");
                _diskWriteCounter = new PerformanceCounter("PhysicalDisk", "Disk Writes/sec", "_Total");
                _networkSentCounter = new PerformanceCounter("Network Interface", "Bytes Sent/sec", "*");
                _networkReceivedCounter = new PerformanceCounter("Network Interface", "Bytes Received/sec", "*");
                
                // Initial read to initialize counters
                _cpuCounter.NextValue();
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to initialize performance counters");
            }
        }
        
        // Start metrics collection timer
        _metricsTimer = new Timer(CollectAndReportMetrics, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
    }

    public async Task<SystemResourceMetrics> GetCurrentMetricsAsync()
    {
        var metrics = new SystemResourceMetrics
        {
            Timestamp = DateTime.UtcNow,
            CpuUsagePercent = GetCpuUsage(),
            MemoryUsageBytes = GetMemoryUsage(),
            MemoryAvailableBytes = GetAvailableMemory(),
            GcHeapSizeBytes = GC.GetTotalMemory(false),
            ThreadCount = _currentProcess.Threads.Count,
            ProcessUptimeSeconds = (DateTime.UtcNow - _currentProcess.StartTime).TotalSeconds,
            FileDescriptorCount = GetFileDescriptorCount(),
            DiskReadIops = GetDiskReadIops(),
            DiskWriteIops = GetDiskWriteIops(),
            NetworkBytesSent = GetNetworkBytesSent(),
            NetworkBytesReceived = GetNetworkBytesReceived()
        };

        return metrics;
    }

    public async Task StartMonitoringAsync(CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting enhanced system resource monitoring");
        await Task.CompletedTask;
    }

    public async Task StopMonitoringAsync()
    {
        _logger.LogInformation("Stopping enhanced system resource monitoring");
        await StopAsync(CancellationToken.None);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Enhanced system resource monitor started");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(10), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }
        
        _logger.LogInformation("Enhanced system resource monitor stopped");
    }

    private void CollectAndReportMetrics(object? state)
    {
        try
        {
            var metrics = GetCurrentMetricsAsync().Result;
            
            // Update Prometheus metrics
            MetricsRegistry.CpuUsagePercent.Set(metrics.CpuUsagePercent);
            MetricsRegistry.MemoryUsageBytes.Set(metrics.MemoryUsageBytes);
            MetricsRegistry.MemoryAvailableBytes.Set(metrics.MemoryAvailableBytes);
            MetricsRegistry.GcHeapSizeBytes.Set(metrics.GcHeapSizeBytes);
            MetricsRegistry.ThreadCount.Set(metrics.ThreadCount);
            MetricsRegistry.ProcessUptimeSeconds.Set(metrics.ProcessUptimeSeconds);
            MetricsRegistry.FileDescriptorCount.Set(metrics.FileDescriptorCount);
            MetricsRegistry.DiskIopsTotal.WithLabels("read").Set(metrics.DiskReadIops);
            MetricsRegistry.DiskIopsTotal.WithLabels("write").Set(metrics.DiskWriteIops);
            
            // Check thresholds and trigger alerts
            CheckThresholds(metrics);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect system resource metrics");
        }
    }

    private void CheckThresholds(SystemResourceMetrics metrics)
    {
        // CPU usage alert
        if (metrics.CpuUsagePercent > _thresholds.CpuUsageWarningPercent)
        {
            var severity = metrics.CpuUsagePercent > _thresholds.CpuUsageCriticalPercent ? "critical" : "warning";
            MetricsRegistry.SystemResourceAlerts.WithLabels("cpu", severity).Inc();
            
            ResourceAlertTriggered?.Invoke(this, new ResourceAlertEventArgs
            {
                ResourceType = "CPU",
                CurrentValue = metrics.CpuUsagePercent,
                Threshold = _thresholds.CpuUsageWarningPercent,
                Severity = severity,
                Message = $"CPU usage is {metrics.CpuUsagePercent:F1}%"
            });
        }

        // Memory usage alert
        var memoryUsagePercent = (metrics.MemoryUsageBytes / (double)(metrics.MemoryUsageBytes + metrics.MemoryAvailableBytes)) * 100;
        if (memoryUsagePercent > _thresholds.MemoryUsageWarningPercent)
        {
            var severity = memoryUsagePercent > _thresholds.MemoryUsageCriticalPercent ? "critical" : "warning";
            MetricsRegistry.SystemResourceAlerts.WithLabels("memory", severity).Inc();
            
            ResourceAlertTriggered?.Invoke(this, new ResourceAlertEventArgs
            {
                ResourceType = "Memory",
                CurrentValue = memoryUsagePercent,
                Threshold = _thresholds.MemoryUsageWarningPercent,
                Severity = severity,
                Message = $"Memory usage is {memoryUsagePercent:F1}%"
            });
        }

        // Thread count alert
        if (metrics.ThreadCount > _thresholds.ThreadCountWarning)
        {
            var severity = metrics.ThreadCount > _thresholds.ThreadCountCritical ? "critical" : "warning";
            MetricsRegistry.SystemResourceAlerts.WithLabels("threads", severity).Inc();
            
            ResourceAlertTriggered?.Invoke(this, new ResourceAlertEventArgs
            {
                ResourceType = "Threads",
                CurrentValue = metrics.ThreadCount,
                Threshold = _thresholds.ThreadCountWarning,
                Severity = severity,
                Message = $"Thread count is {metrics.ThreadCount}"
            });
        }
    }

    private double GetCpuUsage()
    {
        if (_cpuCounter != null && OperatingSystem.IsWindows())
        {
            try
            {
                return _cpuCounter.NextValue();
            }
            catch
            {
                // Fallback to process CPU time calculation
            }
        }
        
        // Cross-platform fallback using process CPU time
        return (_currentProcess.TotalProcessorTime.TotalMilliseconds / Environment.ProcessorCount / 
                (DateTime.UtcNow - _currentProcess.StartTime).TotalMilliseconds) * 100;
    }

    private long GetMemoryUsage()
    {
        return _currentProcess.WorkingSet64;
    }

    private long GetAvailableMemory()
    {
        if (_memoryCounter != null && OperatingSystem.IsWindows())
        {
            try
            {
                return (long)(_memoryCounter.NextValue() * 1024 * 1024); // Convert MB to bytes
            }
            catch { }
        }
        
        // Fallback - estimate based on GC info
        var gcInfo = GC.GetGCMemoryInfo();
        return gcInfo.TotalAvailableMemoryBytes - gcInfo.HeapSizeBytes;
    }

    private int GetFileDescriptorCount()
    {
        // This is platform-specific and complex to implement accurately
        // For now, return handle count on Windows or estimate on other platforms
        if (OperatingSystem.IsWindows())
        {
            return _currentProcess.HandleCount;
        }
        
        return 0; // TODO: Implement for Linux/macOS
    }

    private double GetDiskReadIops()
    {
        if (_diskReadCounter != null && OperatingSystem.IsWindows())
        {
            try
            {
                return _diskReadCounter.NextValue();
            }
            catch { }
        }
        return 0;
    }

    private double GetDiskWriteIops()
    {
        if (_diskWriteCounter != null && OperatingSystem.IsWindows())
        {
            try
            {
                return _diskWriteCounter.NextValue();
            }
            catch { }
        }
        return 0;
    }

    private double GetNetworkBytesSent()
    {
        if (_networkSentCounter != null && OperatingSystem.IsWindows())
        {
            try
            {
                return _networkSentCounter.NextValue();
            }
            catch { }
        }
        return 0;
    }

    private double GetNetworkBytesReceived()
    {
        if (_networkReceivedCounter != null && OperatingSystem.IsWindows())
        {
            try
            {
                return _networkReceivedCounter.NextValue();
            }
            catch { }
        }
        return 0;
    }

    protected override void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            _metricsTimer?.Dispose();
            _cpuCounter?.Dispose();
            _memoryCounter?.Dispose();
            _diskReadCounter?.Dispose();
            _diskWriteCounter?.Dispose();
            _networkSentCounter?.Dispose();
            _networkReceivedCounter?.Dispose();
            _currentProcess?.Dispose();
            _disposed = true;
        }
        base.Dispose(disposing);
    }
}

/// <summary>
/// System resource metrics data model
/// </summary>
public class SystemResourceMetrics
{
    public DateTime Timestamp { get; set; }
    public double CpuUsagePercent { get; set; }
    public long MemoryUsageBytes { get; set; }
    public long MemoryAvailableBytes { get; set; }
    public long GcHeapSizeBytes { get; set; }
    public int ThreadCount { get; set; }
    public double ProcessUptimeSeconds { get; set; }
    public int FileDescriptorCount { get; set; }
    public double DiskReadIops { get; set; }
    public double DiskWriteIops { get; set; }
    public double NetworkBytesSent { get; set; }
    public double NetworkBytesReceived { get; set; }
}

/// <summary>
/// System resource thresholds for alerting
/// </summary>
public class SystemResourceThresholds
{
    public double CpuUsageWarningPercent { get; set; } = 80.0;
    public double CpuUsageCriticalPercent { get; set; } = 95.0;
    public double MemoryUsageWarningPercent { get; set; } = 85.0;
    public double MemoryUsageCriticalPercent { get; set; } = 95.0;
    public int ThreadCountWarning { get; set; } = 100;
    public int ThreadCountCritical { get; set; } = 200;
    public double DiskIopsWarning { get; set; } = 1000;
    public double DiskIopsCritical { get; set; } = 2000;
    public double NetworkThroughputWarningMbps { get; set; } = 100;
    public double NetworkThroughputCriticalMbps { get; set; } = 500;
}

/// <summary>
/// Resource alert event arguments
/// </summary>
public class ResourceAlertEventArgs : EventArgs
{
    public string ResourceType { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public double Threshold { get; set; }
    public string Severity { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; } = DateTime.UtcNow;
}

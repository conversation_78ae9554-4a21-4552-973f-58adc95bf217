using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Text.Json;

namespace SmaTrendFollower.Services;

/// <summary>
/// Real-time performance dashboard service
/// Aggregates performance data from all monitoring services and provides dashboard API
/// </summary>
public interface IRealTimePerformanceDashboard : IDisposable
{
    /// <summary>
    /// Gets current dashboard data
    /// </summary>
    Task<DashboardData> GetDashboardDataAsync();
    
    /// <summary>
    /// Gets performance alerts
    /// </summary>
    Task<List<PerformanceAlert>> GetActiveAlertsAsync();
    
    /// <summary>
    /// Gets bottleneck analysis
    /// </summary>
    Task<BottleneckSummary> GetBottleneckSummaryAsync();
    
    /// <summary>
    /// Starts the dashboard service
    /// </summary>
    Task StartAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stops the dashboard service
    /// </summary>
    Task StopAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Real-time performance dashboard implementation
/// </summary>
public sealed class RealTimePerformanceDashboard : BackgroundService, IRealTimePerformanceDashboard
{
    private readonly ILogger<RealTimePerformanceDashboard> _logger;
    private readonly IEnhancedSystemResourceMonitor? _systemMonitor;
    private readonly IDatabasePerformanceMonitor? _databaseMonitor;
    private readonly IWebSocketPerformanceMonitor? _webSocketMonitor;
    private readonly ITradingPipelinePerformanceMonitor? _pipelineMonitor;
    private readonly ConcurrentQueue<PerformanceAlert> _activeAlerts;
    private readonly Timer _dashboardUpdateTimer;
    private DashboardData _currentDashboard;
    private bool _disposed;

    public RealTimePerformanceDashboard(
        ILogger<RealTimePerformanceDashboard> logger,
        IEnhancedSystemResourceMonitor? systemMonitor = null,
        IDatabasePerformanceMonitor? databaseMonitor = null,
        IWebSocketPerformanceMonitor? webSocketMonitor = null,
        ITradingPipelinePerformanceMonitor? pipelineMonitor = null)
    {
        _logger = logger;
        _systemMonitor = systemMonitor;
        _databaseMonitor = databaseMonitor;
        _webSocketMonitor = webSocketMonitor;
        _pipelineMonitor = pipelineMonitor;
        _activeAlerts = new ConcurrentQueue<PerformanceAlert>();
        _currentDashboard = new DashboardData();
        
        // Subscribe to resource alerts if available
        if (_systemMonitor != null)
        {
            _systemMonitor.ResourceAlertTriggered += OnResourceAlert;
        }
        
        // Start dashboard update timer
        _dashboardUpdateTimer = new Timer(UpdateDashboard, null, TimeSpan.FromSeconds(5), TimeSpan.FromSeconds(5));
    }

    public async Task<DashboardData> GetDashboardDataAsync()
    {
        return await Task.FromResult(_currentDashboard);
    }

    public async Task<List<PerformanceAlert>> GetActiveAlertsAsync()
    {
        var alerts = new List<PerformanceAlert>();
        var cutoff = DateTime.UtcNow.AddMinutes(-30); // Only show alerts from last 30 minutes
        
        while (_activeAlerts.TryDequeue(out var alert))
        {
            if (alert.Timestamp > cutoff)
            {
                alerts.Add(alert);
            }
        }
        
        // Re-queue recent alerts
        foreach (var alert in alerts)
        {
            _activeAlerts.Enqueue(alert);
        }
        
        return await Task.FromResult(alerts.OrderByDescending(a => a.Timestamp).ToList());
    }

    public async Task<BottleneckSummary> GetBottleneckSummaryAsync()
    {
        var summary = new BottleneckSummary
        {
            Timestamp = DateTime.UtcNow
        };
        
        if (_pipelineMonitor != null)
        {
            var analysis = await _pipelineMonitor.AnalyzeBottlenecksAsync();
            summary.TotalBottlenecks = analysis.TotalBottlenecks;
            summary.BottlenecksByStage = analysis.BottlenecksByStage;
            summary.Recommendations = analysis.Recommendations;
        }
        
        return summary;
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Real-time performance dashboard started");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromSeconds(30), stoppingToken);
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }
        
        _logger.LogInformation("Real-time performance dashboard stopped");
    }

    private async void UpdateDashboard(object? state)
    {
        try
        {
            var dashboard = new DashboardData
            {
                Timestamp = DateTime.UtcNow
            };
            
            // Collect system metrics
            if (_systemMonitor != null)
            {
                dashboard.SystemMetrics = await _systemMonitor.GetCurrentMetricsAsync();
            }
            
            // Collect database metrics
            if (_databaseMonitor != null)
            {
                dashboard.DatabaseMetrics = await _databaseMonitor.GetPerformanceMetricsAsync();
            }
            
            // Collect WebSocket metrics
            if (_webSocketMonitor != null)
            {
                dashboard.WebSocketMetrics = await _webSocketMonitor.GetPerformanceMetricsAsync();
            }
            
            // Collect pipeline metrics
            if (_pipelineMonitor != null)
            {
                dashboard.PipelineMetrics = await _pipelineMonitor.GetPipelineMetricsAsync();
            }
            
            // Calculate health score
            dashboard.OverallHealthScore = CalculateHealthScore(dashboard);
            
            _currentDashboard = dashboard;
            
            _logger.LogDebug("Dashboard updated - Health Score: {HealthScore:F1}%", dashboard.OverallHealthScore);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to update dashboard data");
        }
    }

    private void OnResourceAlert(object? sender, ResourceAlertEventArgs e)
    {
        var alert = new PerformanceAlert
        {
            Timestamp = e.Timestamp,
            AlertType = "Resource",
            Severity = e.Severity,
            Source = e.ResourceType,
            Message = e.Message,
            CurrentValue = e.CurrentValue,
            Threshold = e.Threshold
        };
        
        _activeAlerts.Enqueue(alert);
        
        // Keep only recent alerts (last 100)
        while (_activeAlerts.Count > 100)
        {
            _activeAlerts.TryDequeue(out _);
        }
        
        _logger.LogWarning("Performance alert: {AlertType} - {Message}", alert.AlertType, alert.Message);
    }

    private double CalculateHealthScore(DashboardData dashboard)
    {
        var scores = new List<double>();
        
        // System health score (0-100)
        if (dashboard.SystemMetrics != null)
        {
            var systemScore = 100.0;
            
            // CPU penalty
            if (dashboard.SystemMetrics.CpuUsagePercent > 80)
                systemScore -= (dashboard.SystemMetrics.CpuUsagePercent - 80) * 2;
            
            // Memory penalty
            var memoryUsagePercent = dashboard.SystemMetrics.MemoryUsageBytes / 
                (double)(dashboard.SystemMetrics.MemoryUsageBytes + dashboard.SystemMetrics.MemoryAvailableBytes) * 100;
            if (memoryUsagePercent > 85)
                systemScore -= (memoryUsagePercent - 85) * 3;
            
            // Thread count penalty
            if (dashboard.SystemMetrics.ThreadCount > 100)
                systemScore -= (dashboard.SystemMetrics.ThreadCount - 100) * 0.5;
            
            scores.Add(Math.Max(0, systemScore));
        }
        
        // WebSocket health score
        if (dashboard.WebSocketMetrics != null)
        {
            var wsScore = 100.0;
            var totalConnections = dashboard.WebSocketMetrics.ConnectionMetrics.Count;
            var activeConnections = dashboard.WebSocketMetrics.ConnectionMetrics.Values
                .Count(c => c.State == System.Net.WebSockets.WebSocketState.Open);
            
            if (totalConnections > 0)
            {
                var connectionRatio = (double)activeConnections / totalConnections;
                wsScore = connectionRatio * 100;
            }
            
            scores.Add(wsScore);
        }
        
        // Pipeline health score
        if (dashboard.PipelineMetrics != null)
        {
            var pipelineScore = 100.0;
            
            // Penalty for too many active executions
            if (dashboard.PipelineMetrics.ActiveExecutions > 50)
                pipelineScore -= (dashboard.PipelineMetrics.ActiveExecutions - 50) * 2;
            
            // Penalty for recent bottlenecks
            var recentBottlenecks = dashboard.PipelineMetrics.RecentBottlenecks.Count;
            if (recentBottlenecks > 5)
                pipelineScore -= (recentBottlenecks - 5) * 5;
            
            scores.Add(Math.Max(0, pipelineScore));
        }
        
        return scores.Count > 0 ? scores.Average() : 100.0;
    }

    public override void Dispose()
    {
        if (!_disposed)
        {
            _dashboardUpdateTimer?.Dispose();
            
            if (_systemMonitor != null)
            {
                _systemMonitor.ResourceAlertTriggered -= OnResourceAlert;
            }
            
            _disposed = true;
        }
        
        base.Dispose();
    }
}

/// <summary>
/// Dashboard data model
/// </summary>
public class DashboardData
{
    public DateTime Timestamp { get; set; }
    public double OverallHealthScore { get; set; }
    public SystemResourceMetrics? SystemMetrics { get; set; }
    public DatabasePerformanceMetrics? DatabaseMetrics { get; set; }
    public WebSocketPerformanceMetrics? WebSocketMetrics { get; set; }
    public TradingPipelineMetrics? PipelineMetrics { get; set; }
}

/// <summary>
/// Performance alert model
/// </summary>
public class PerformanceAlert
{
    public DateTime Timestamp { get; set; }
    public string AlertType { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
    public string Source { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public double CurrentValue { get; set; }
    public double Threshold { get; set; }
}

/// <summary>
/// Bottleneck summary model
/// </summary>
public class BottleneckSummary
{
    public DateTime Timestamp { get; set; }
    public int TotalBottlenecks { get; set; }
    public Dictionary<string, int> BottlenecksByStage { get; set; } = new();
    public List<string> Recommendations { get; set; } = new();
}

using Microsoft.Extensions.Logging;
using SmaTrendFollower.Monitoring;
using StackExchange.Redis;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Database performance monitoring service for SQLite and Redis
/// Tracks query latency, connection pool usage, cache hit rates, and transaction throughput
/// </summary>
public interface IDatabasePerformanceMonitor : IDisposable
{
    /// <summary>
    /// Tracks a database operation
    /// </summary>
    Task<T> TrackDatabaseOperationAsync<T>(string database, string operation, Func<Task<T>> operationFunc);
    
    /// <summary>
    /// Tracks a Redis operation
    /// </summary>
    Task<T> TrackRedisOperationAsync<T>(string operation, int database, Func<Task<T>> operationFunc);
    
    /// <summary>
    /// Records a cache hit or miss
    /// </summary>
    void RecordCacheOperation(string operation, bool hit);
    
    /// <summary>
    /// Gets current database performance metrics
    /// </summary>
    Task<DatabasePerformanceMetrics> GetPerformanceMetricsAsync();
    
    /// <summary>
    /// Starts monitoring Redis server metrics
    /// </summary>
    Task StartRedisMonitoringAsync(IConnectionMultiplexer redis);
}

/// <summary>
/// Database performance monitoring service implementation
/// </summary>
public sealed class DatabasePerformanceMonitor : IDatabasePerformanceMonitor
{
    private readonly ILogger<DatabasePerformanceMonitor> _logger;
    private readonly ConcurrentDictionary<string, DatabaseOperationMetrics> _operationMetrics;
    private readonly Timer _metricsTimer;
    private IConnectionMultiplexer? _redis;
    private bool _disposed;

    public DatabasePerformanceMonitor(ILogger<DatabasePerformanceMonitor> logger)
    {
        _logger = logger;
        _operationMetrics = new ConcurrentDictionary<string, DatabaseOperationMetrics>();
        
        // Start metrics collection timer
        _metricsTimer = new Timer(CollectAndReportMetrics, null, TimeSpan.FromSeconds(10), TimeSpan.FromSeconds(10));
    }

    public async Task<T> TrackDatabaseOperationAsync<T>(string database, string operation, Func<Task<T>> operationFunc)
    {
        var stopwatch = Stopwatch.StartNew();
        var key = $"{database}:{operation}";
        
        try
        {
            var result = await operationFunc().ConfigureAwait(false);
            stopwatch.Stop();
            
            RecordOperationSuccess(key, stopwatch.Elapsed.TotalMilliseconds);
            
            // Update Prometheus metrics
            MetricsRegistry.DatabaseQueryDurationMs
                .WithLabels(database, operation)
                .Observe(stopwatch.Elapsed.TotalMilliseconds);
            
            MetricsRegistry.DatabaseOperationsTotal
                .WithLabels(database, operation, "success")
                .Inc();
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            RecordOperationError(key, stopwatch.Elapsed.TotalMilliseconds, ex);
            
            MetricsRegistry.DatabaseOperationsTotal
                .WithLabels(database, operation, "error")
                .Inc();
            
            throw;
        }
    }

    public async Task<T> TrackRedisOperationAsync<T>(string operation, int database, Func<Task<T>> operationFunc)
    {
        var stopwatch = Stopwatch.StartNew();
        var key = $"redis:{operation}";
        
        try
        {
            var result = await operationFunc().ConfigureAwait(false);
            stopwatch.Stop();
            
            RecordOperationSuccess(key, stopwatch.Elapsed.TotalMilliseconds);
            
            // Update Prometheus metrics
            MetricsRegistry.RedisOperationDurationMs
                .WithLabels(operation, database.ToString())
                .Observe(stopwatch.Elapsed.TotalMilliseconds);
            
            return result;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            
            RecordOperationError(key, stopwatch.Elapsed.TotalMilliseconds, ex);
            
            throw;
        }
    }

    public void RecordCacheOperation(string operation, bool hit)
    {
        var result = hit ? "hit" : "miss";
        MetricsRegistry.RedisCacheOperations
            .WithLabels(operation, result)
            .Inc();
    }

    public async Task<DatabasePerformanceMetrics> GetPerformanceMetricsAsync()
    {
        var metrics = new DatabasePerformanceMetrics
        {
            Timestamp = DateTime.UtcNow,
            OperationMetrics = _operationMetrics.ToDictionary(kvp => kvp.Key, kvp => kvp.Value.Clone())
        };

        // Get Redis server info if available
        if (_redis != null)
        {
            try
            {
                var server = _redis.GetServer(_redis.GetEndPoints().First());
                var info = await server.InfoAsync("memory");
                
                if (info != null)
                {
                    foreach (var item in info)
                    {
                        if (item.Key == "used_memory")
                        {
                            if (long.TryParse(item.Value, out var memoryUsage))
                            {
                                metrics.RedisMemoryUsageBytes = memoryUsage;
                                MetricsRegistry.RedisMemoryUsageBytes.Set(memoryUsage);
                            }
                        }
                    }
                }
                
                // Get key count for common patterns
                var database = _redis.GetDatabase();
                var keyPatterns = new[] { "signal:*", "universe:*", "vix:*", "regime:*", "stops:*" };
                
                foreach (var pattern in keyPatterns)
                {
                    try
                    {
                        var keys = server.Keys(pattern: pattern).Take(1000).Count();
                        MetricsRegistry.RedisKeyCount.WithLabels(pattern).Set(keys);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogWarning(ex, "Failed to count Redis keys for pattern {Pattern}", pattern);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to get Redis server info");
            }
        }

        return metrics;
    }

    public async Task StartRedisMonitoringAsync(IConnectionMultiplexer redis)
    {
        _redis = redis;
        _logger.LogInformation("Started Redis performance monitoring");
        await Task.CompletedTask;
    }

    private void RecordOperationSuccess(string key, double latencyMs)
    {
        _operationMetrics.AddOrUpdate(key,
            new DatabaseOperationMetrics { OperationName = key },
            (_, existing) =>
            {
                existing.TotalOperations++;
                existing.TotalLatencyMs += latencyMs;
                existing.LastOperationTime = DateTime.UtcNow;
                return existing;
            });
    }

    private void RecordOperationError(string key, double latencyMs, Exception ex)
    {
        _operationMetrics.AddOrUpdate(key,
            new DatabaseOperationMetrics { OperationName = key },
            (_, existing) =>
            {
                existing.TotalOperations++;
                existing.ErrorCount++;
                existing.TotalLatencyMs += latencyMs;
                existing.LastError = ex.Message;
                existing.LastOperationTime = DateTime.UtcNow;
                return existing;
            });
    }

    private void CollectAndReportMetrics(object? state)
    {
        try
        {
            // Update connection count metrics
            if (_redis != null)
            {
                var connectionCount = _redis.GetCounters().Interactive.SocketCount;
                MetricsRegistry.RedisConnections.Set(connectionCount);
            }
            
            // Log performance summary
            var totalOps = _operationMetrics.Values.Sum(m => m.TotalOperations);
            var avgLatency = _operationMetrics.Values
                .Where(m => m.TotalOperations > 0)
                .Average(m => m.AverageLatencyMs);
            
            if (totalOps > 0)
            {
                _logger.LogDebug("Database performance: {TotalOps} operations, {AvgLatency:F1}ms avg latency",
                    totalOps, avgLatency);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to collect database performance metrics");
        }
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _metricsTimer?.Dispose();
            _disposed = true;
        }
    }
}

/// <summary>
/// Database performance metrics data model
/// </summary>
public class DatabasePerformanceMetrics
{
    public DateTime Timestamp { get; set; }
    public Dictionary<string, DatabaseOperationMetrics> OperationMetrics { get; set; } = new();
    public long RedisMemoryUsageBytes { get; set; }
    public int RedisConnectionCount { get; set; }
}

/// <summary>
/// Individual database operation metrics
/// </summary>
public class DatabaseOperationMetrics
{
    public string OperationName { get; set; } = string.Empty;
    public long TotalOperations { get; set; }
    public long ErrorCount { get; set; }
    public double TotalLatencyMs { get; set; }
    public DateTime LastOperationTime { get; set; }
    public string? LastError { get; set; }
    
    public double AverageLatencyMs => TotalOperations > 0 ? TotalLatencyMs / TotalOperations : 0;
    public double ErrorRate => TotalOperations > 0 ? (double)ErrorCount / TotalOperations : 0;
    
    public DatabaseOperationMetrics Clone()
    {
        return new DatabaseOperationMetrics
        {
            OperationName = OperationName,
            TotalOperations = TotalOperations,
            ErrorCount = ErrorCount,
            TotalLatencyMs = TotalLatencyMs,
            LastOperationTime = LastOperationTime,
            LastError = LastError
        };
    }
}

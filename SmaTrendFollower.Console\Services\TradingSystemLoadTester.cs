using Microsoft.Extensions.Logging;
using SmaTrendFollower.Models;
using System.Collections.Concurrent;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Load testing service for trading system performance validation
/// Simulates peak trading conditions to identify bottlenecks and breaking points
/// </summary>
public interface ITradingSystemLoadTester : IDisposable
{
    /// <summary>
    /// Runs a comprehensive load test
    /// </summary>
    Task<LoadTestResult> RunLoadTestAsync(LoadTestConfiguration config, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Runs signal generation load test
    /// </summary>
    Task<LoadTestResult> RunSignalGenerationLoadTestAsync(int concurrentSignals, int durationMinutes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Runs WebSocket connection stress test
    /// </summary>
    Task<LoadTestResult> RunWebSocketStressTestAsync(int connectionCount, int messagesPerSecond, int durationMinutes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Runs database performance test
    /// </summary>
    Task<LoadTestResult> RunDatabaseLoadTestAsync(int concurrentOperations, int operationsPerSecond, int durationMinutes, CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Runs API rate limit stress test
    /// </summary>
    Task<LoadTestResult> RunApiStressTestAsync(string service, int requestsPerSecond, int durationMinutes, CancellationToken cancellationToken = default);
}

/// <summary>
/// Trading system load tester implementation
/// </summary>
public sealed class TradingSystemLoadTester : ITradingSystemLoadTester
{
    private readonly ILogger<TradingSystemLoadTester> _logger;
    private readonly ISignalGenerator? _signalGenerator;
    private readonly IWebSocketPerformanceMonitor? _webSocketMonitor;
    private readonly IDatabasePerformanceMonitor? _databaseMonitor;
    private readonly ITradingPipelinePerformanceMonitor? _pipelineMonitor;
    private readonly ConcurrentBag<LoadTestMetric> _testMetrics;
    private bool _disposed;

    public TradingSystemLoadTester(
        ILogger<TradingSystemLoadTester> logger,
        ISignalGenerator? signalGenerator = null,
        IWebSocketPerformanceMonitor? webSocketMonitor = null,
        IDatabasePerformanceMonitor? databaseMonitor = null,
        ITradingPipelinePerformanceMonitor? pipelineMonitor = null)
    {
        _logger = logger;
        _signalGenerator = signalGenerator;
        _webSocketMonitor = webSocketMonitor;
        _databaseMonitor = databaseMonitor;
        _pipelineMonitor = pipelineMonitor;
        _testMetrics = new ConcurrentBag<LoadTestMetric>();
    }

    public async Task<LoadTestResult> RunLoadTestAsync(LoadTestConfiguration config, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting comprehensive load test with configuration: {Config}", config);
        
        var startTime = DateTime.UtcNow;
        var results = new List<LoadTestResult>();
        
        try
        {
            // Run signal generation test
            if (config.TestSignalGeneration)
            {
                var signalResult = await RunSignalGenerationLoadTestAsync(
                    config.ConcurrentSignals, config.DurationMinutes, cancellationToken);
                results.Add(signalResult);
            }
            
            // Run WebSocket test
            if (config.TestWebSockets)
            {
                var wsResult = await RunWebSocketStressTestAsync(
                    config.WebSocketConnections, config.MessagesPerSecond, config.DurationMinutes, cancellationToken);
                results.Add(wsResult);
            }
            
            // Run database test
            if (config.TestDatabase)
            {
                var dbResult = await RunDatabaseLoadTestAsync(
                    config.ConcurrentDatabaseOps, config.DatabaseOpsPerSecond, config.DurationMinutes, cancellationToken);
                results.Add(dbResult);
            }
            
            // Aggregate results
            var aggregateResult = new LoadTestResult
            {
                TestName = "Comprehensive Load Test",
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Success = results.All(r => r.Success),
                TotalOperations = results.Sum(r => r.TotalOperations),
                SuccessfulOperations = results.Sum(r => r.SuccessfulOperations),
                FailedOperations = results.Sum(r => r.FailedOperations),
                AverageLatencyMs = results.Where(r => r.TotalOperations > 0).Average(r => r.AverageLatencyMs),
                MaxLatencyMs = results.Max(r => r.MaxLatencyMs),
                MinLatencyMs = results.Min(r => r.MinLatencyMs),
                ThroughputPerSecond = results.Sum(r => r.ThroughputPerSecond),
                ErrorRate = results.Sum(r => r.FailedOperations) / (double)results.Sum(r => r.TotalOperations),
                Details = string.Join("; ", results.Select(r => $"{r.TestName}: {r.ThroughputPerSecond:F1} ops/sec"))
            };
            
            _logger.LogInformation("Comprehensive load test completed: {Success}, {TotalOps} operations, {Throughput:F1} ops/sec",
                aggregateResult.Success, aggregateResult.TotalOperations, aggregateResult.ThroughputPerSecond);
            
            return aggregateResult;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Load test failed");
            return new LoadTestResult
            {
                TestName = "Comprehensive Load Test",
                StartTime = startTime,
                EndTime = DateTime.UtcNow,
                Success = false,
                ErrorMessage = ex.Message
            };
        }
    }

    public async Task<LoadTestResult> RunSignalGenerationLoadTestAsync(int concurrentSignals, int durationMinutes, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting signal generation load test: {ConcurrentSignals} concurrent, {Duration} minutes",
            concurrentSignals, durationMinutes);
        
        var startTime = DateTime.UtcNow;
        var endTime = startTime.AddMinutes(durationMinutes);
        var metrics = new ConcurrentBag<LoadTestMetric>();
        var semaphore = new SemaphoreSlim(concurrentSignals);
        
        var tasks = new List<Task>();
        
        while (DateTime.UtcNow < endTime && !cancellationToken.IsCancellationRequested)
        {
            await semaphore.WaitAsync(cancellationToken);
            
            var task = Task.Run(async () =>
            {
                try
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    // Simulate signal generation
                    if (_signalGenerator != null)
                    {
                        var symbols = GenerateTestSymbols(10);
                        var signals = await _signalGenerator.GenerateSignalsAsync(symbols, cancellationToken);
                        
                        stopwatch.Stop();
                        
                        metrics.Add(new LoadTestMetric
                        {
                            Timestamp = DateTime.UtcNow,
                            OperationType = "SignalGeneration",
                            LatencyMs = stopwatch.Elapsed.TotalMilliseconds,
                            Success = signals?.Any() == true,
                            Details = $"Generated {signals?.Count() ?? 0} signals"
                        });
                    }
                    else
                    {
                        // Simulate work if no signal generator available
                        await Task.Delay(Random.Shared.Next(100, 500), cancellationToken);
                        stopwatch.Stop();
                        
                        metrics.Add(new LoadTestMetric
                        {
                            Timestamp = DateTime.UtcNow,
                            OperationType = "SimulatedSignalGeneration",
                            LatencyMs = stopwatch.Elapsed.TotalMilliseconds,
                            Success = true,
                            Details = "Simulated signal generation"
                        });
                    }
                }
                catch (Exception ex)
                {
                    metrics.Add(new LoadTestMetric
                    {
                        Timestamp = DateTime.UtcNow,
                        OperationType = "SignalGeneration",
                        LatencyMs = 0,
                        Success = false,
                        Details = ex.Message
                    });
                }
                finally
                {
                    semaphore.Release();
                }
            }, cancellationToken);
            
            tasks.Add(task);
            
            // Limit concurrent tasks
            if (tasks.Count >= concurrentSignals * 2)
            {
                await Task.WhenAny(tasks);
                tasks.RemoveAll(t => t.IsCompleted);
            }
            
            // Small delay to control rate
            await Task.Delay(100, cancellationToken);
        }
        
        // Wait for remaining tasks
        await Task.WhenAll(tasks);
        
        return AnalyzeMetrics("Signal Generation Load Test", startTime, metrics.ToList());
    }

    public async Task<LoadTestResult> RunWebSocketStressTestAsync(int connectionCount, int messagesPerSecond, int durationMinutes, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting WebSocket stress test: {Connections} connections, {MessagesPerSec} msg/sec, {Duration} minutes",
            connectionCount, messagesPerSecond, durationMinutes);
        
        var startTime = DateTime.UtcNow;
        var endTime = startTime.AddMinutes(durationMinutes);
        var metrics = new ConcurrentBag<LoadTestMetric>();
        
        // Simulate WebSocket message processing
        var tasks = Enumerable.Range(0, connectionCount).Select(async connectionId =>
        {
            var messageInterval = TimeSpan.FromMilliseconds(1000.0 / messagesPerSecond * connectionCount);
            
            while (DateTime.UtcNow < endTime && !cancellationToken.IsCancellationRequested)
            {
                var stopwatch = Stopwatch.StartNew();
                
                try
                {
                    // Simulate message processing
                    await Task.Delay(Random.Shared.Next(1, 10), cancellationToken);
                    
                    if (_webSocketMonitor != null)
                    {
                        _webSocketMonitor.RecordMessageReceived("LoadTest", "TestMessage", 1024);
                    }
                    
                    stopwatch.Stop();
                    
                    metrics.Add(new LoadTestMetric
                    {
                        Timestamp = DateTime.UtcNow,
                        OperationType = "WebSocketMessage",
                        LatencyMs = stopwatch.Elapsed.TotalMilliseconds,
                        Success = true,
                        Details = $"Connection {connectionId}"
                    });
                }
                catch (Exception ex)
                {
                    stopwatch.Stop();
                    
                    metrics.Add(new LoadTestMetric
                    {
                        Timestamp = DateTime.UtcNow,
                        OperationType = "WebSocketMessage",
                        LatencyMs = stopwatch.Elapsed.TotalMilliseconds,
                        Success = false,
                        Details = ex.Message
                    });
                }
                
                await Task.Delay(messageInterval, cancellationToken);
            }
        });
        
        await Task.WhenAll(tasks);
        
        return AnalyzeMetrics("WebSocket Stress Test", startTime, metrics.ToList());
    }

    public async Task<LoadTestResult> RunDatabaseLoadTestAsync(int concurrentOperations, int operationsPerSecond, int durationMinutes, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting database load test: {ConcurrentOps} concurrent, {OpsPerSec} ops/sec, {Duration} minutes",
            concurrentOperations, operationsPerSecond, durationMinutes);
        
        var startTime = DateTime.UtcNow;
        var endTime = startTime.AddMinutes(durationMinutes);
        var metrics = new ConcurrentBag<LoadTestMetric>();
        var semaphore = new SemaphoreSlim(concurrentOperations);
        
        var operationInterval = TimeSpan.FromMilliseconds(1000.0 / operationsPerSecond);
        
        while (DateTime.UtcNow < endTime && !cancellationToken.IsCancellationRequested)
        {
            await semaphore.WaitAsync(cancellationToken);
            
            _ = Task.Run(async () =>
            {
                try
                {
                    var stopwatch = Stopwatch.StartNew();
                    
                    // Simulate database operation
                    if (_databaseMonitor != null)
                    {
                        await _databaseMonitor.TrackDatabaseOperationAsync("LoadTest", "SELECT", async () =>
                        {
                            await Task.Delay(Random.Shared.Next(1, 50), cancellationToken);
                            return "test_result";
                        });
                    }
                    else
                    {
                        await Task.Delay(Random.Shared.Next(1, 50), cancellationToken);
                    }
                    
                    stopwatch.Stop();
                    
                    metrics.Add(new LoadTestMetric
                    {
                        Timestamp = DateTime.UtcNow,
                        OperationType = "DatabaseOperation",
                        LatencyMs = stopwatch.Elapsed.TotalMilliseconds,
                        Success = true,
                        Details = "Simulated database query"
                    });
                }
                catch (Exception ex)
                {
                    metrics.Add(new LoadTestMetric
                    {
                        Timestamp = DateTime.UtcNow,
                        OperationType = "DatabaseOperation",
                        LatencyMs = 0,
                        Success = false,
                        Details = ex.Message
                    });
                }
                finally
                {
                    semaphore.Release();
                }
            }, cancellationToken);
            
            await Task.Delay(operationInterval, cancellationToken);
        }
        
        return AnalyzeMetrics("Database Load Test", startTime, metrics.ToList());
    }

    public async Task<LoadTestResult> RunApiStressTestAsync(string service, int requestsPerSecond, int durationMinutes, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Starting API stress test for {Service}: {RequestsPerSec} req/sec, {Duration} minutes",
            service, requestsPerSecond, durationMinutes);
        
        var startTime = DateTime.UtcNow;
        var endTime = startTime.AddMinutes(durationMinutes);
        var metrics = new ConcurrentBag<LoadTestMetric>();
        
        var requestInterval = TimeSpan.FromMilliseconds(1000.0 / requestsPerSecond);
        
        while (DateTime.UtcNow < endTime && !cancellationToken.IsCancellationRequested)
        {
            var stopwatch = Stopwatch.StartNew();
            
            try
            {
                // Simulate API request
                await Task.Delay(Random.Shared.Next(10, 200), cancellationToken);
                
                stopwatch.Stop();
                
                metrics.Add(new LoadTestMetric
                {
                    Timestamp = DateTime.UtcNow,
                    OperationType = "ApiRequest",
                    LatencyMs = stopwatch.Elapsed.TotalMilliseconds,
                    Success = Random.Shared.NextDouble() > 0.05, // 5% failure rate
                    Details = $"API call to {service}"
                });
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                
                metrics.Add(new LoadTestMetric
                {
                    Timestamp = DateTime.UtcNow,
                    OperationType = "ApiRequest",
                    LatencyMs = stopwatch.Elapsed.TotalMilliseconds,
                    Success = false,
                    Details = ex.Message
                });
            }
            
            await Task.Delay(requestInterval, cancellationToken);
        }
        
        return AnalyzeMetrics($"API Stress Test ({service})", startTime, metrics.ToList());
    }

    private LoadTestResult AnalyzeMetrics(string testName, DateTime startTime, List<LoadTestMetric> metrics)
    {
        var endTime = DateTime.UtcNow;
        var duration = endTime - startTime;
        
        var successfulOps = metrics.Count(m => m.Success);
        var failedOps = metrics.Count(m => !m.Success);
        var totalOps = metrics.Count;
        
        var latencies = metrics.Where(m => m.Success).Select(m => m.LatencyMs).ToList();
        
        return new LoadTestResult
        {
            TestName = testName,
            StartTime = startTime,
            EndTime = endTime,
            Success = failedOps == 0,
            TotalOperations = totalOps,
            SuccessfulOperations = successfulOps,
            FailedOperations = failedOps,
            AverageLatencyMs = latencies.Any() ? latencies.Average() : 0,
            MaxLatencyMs = latencies.Any() ? latencies.Max() : 0,
            MinLatencyMs = latencies.Any() ? latencies.Min() : 0,
            ThroughputPerSecond = totalOps / duration.TotalSeconds,
            ErrorRate = totalOps > 0 ? (double)failedOps / totalOps : 0,
            Details = $"Duration: {duration.TotalMinutes:F1} minutes"
        };
    }

    private List<string> GenerateTestSymbols(int count)
    {
        var symbols = new[] { "AAPL", "MSFT", "GOOGL", "AMZN", "TSLA", "META", "NVDA", "NFLX", "CRM", "ADBE" };
        return symbols.Take(count).ToList();
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            _disposed = true;
        }
    }
}

/// <summary>
/// Load test configuration
/// </summary>
public class LoadTestConfiguration
{
    public bool TestSignalGeneration { get; set; } = true;
    public bool TestWebSockets { get; set; } = true;
    public bool TestDatabase { get; set; } = true;
    public bool TestApis { get; set; } = true;

    public int DurationMinutes { get; set; } = 5;
    public int ConcurrentSignals { get; set; } = 10;
    public int WebSocketConnections { get; set; } = 5;
    public int MessagesPerSecond { get; set; } = 100;
    public int ConcurrentDatabaseOps { get; set; } = 20;
    public int DatabaseOpsPerSecond { get; set; } = 50;
    public int ApiRequestsPerSecond { get; set; } = 30;

    public override string ToString()
    {
        return $"Duration: {DurationMinutes}min, Signals: {ConcurrentSignals}, WS: {WebSocketConnections}, DB: {ConcurrentDatabaseOps}";
    }
}

/// <summary>
/// Load test result
/// </summary>
public class LoadTestResult
{
    public string TestName { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public DateTime EndTime { get; set; }
    public bool Success { get; set; }
    public int TotalOperations { get; set; }
    public int SuccessfulOperations { get; set; }
    public int FailedOperations { get; set; }
    public double AverageLatencyMs { get; set; }
    public double MaxLatencyMs { get; set; }
    public double MinLatencyMs { get; set; }
    public double ThroughputPerSecond { get; set; }
    public double ErrorRate { get; set; }
    public string? ErrorMessage { get; set; }
    public string? Details { get; set; }

    public TimeSpan Duration => EndTime - StartTime;

    public override string ToString()
    {
        return $"{TestName}: {Success} - {TotalOperations} ops in {Duration.TotalMinutes:F1}min " +
               $"({ThroughputPerSecond:F1} ops/sec, {ErrorRate:P1} errors)";
    }
}

/// <summary>
/// Individual load test metric
/// </summary>
public class LoadTestMetric
{
    public DateTime Timestamp { get; set; }
    public string OperationType { get; set; } = string.Empty;
    public double LatencyMs { get; set; }
    public bool Success { get; set; }
    public string? Details { get; set; }
}

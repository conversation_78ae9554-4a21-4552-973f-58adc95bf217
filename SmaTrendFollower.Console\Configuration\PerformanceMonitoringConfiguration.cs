using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using SmaTrendFollower.Services;

namespace SmaTrendFollower.Configuration;

/// <summary>
/// Configuration extensions for performance monitoring services
/// </summary>
public static class PerformanceMonitoringConfiguration
{
    /// <summary>
    /// Adds comprehensive performance monitoring services to the DI container
    /// </summary>
    public static IServiceCollection AddPerformanceMonitoring(this IServiceCollection services)
    {
        // Core monitoring services
        services.AddSingleton<IEnhancedSystemResourceMonitor, EnhancedSystemResourceMonitor>();
        services.AddSingleton<IDatabasePerformanceMonitor, DatabasePerformanceMonitor>();
        services.AddSingleton<IWebSocketPerformanceMonitor, WebSocketPerformanceMonitor>();
        services.AddSingleton<ITradingPipelinePerformanceMonitor, TradingPipelinePerformanceMonitor>();
        
        // Dashboard and alerting
        services.AddSingleton<IRealTimePerformanceDashboard, RealTimePerformanceDashboard>();
        services.AddSingleton<IPerformanceAlertingService, PerformanceAlertingService>();
        
        // Load testing
        services.AddSingleton<ITradingSystemLoadTester, TradingSystemLoadTester>();
        
        // Register as hosted services for background monitoring
        services.AddHostedService<EnhancedSystemResourceMonitor>(provider => 
            (EnhancedSystemResourceMonitor)provider.GetRequiredService<IEnhancedSystemResourceMonitor>());
        
        services.AddHostedService<RealTimePerformanceDashboard>(provider => 
            (RealTimePerformanceDashboard)provider.GetRequiredService<IRealTimePerformanceDashboard>());
        
        services.AddHostedService<PerformanceAlertingService>(provider => 
            (PerformanceAlertingService)provider.GetRequiredService<IPerformanceAlertingService>());
        
        return services;
    }
    
    /// <summary>
    /// Configures performance monitoring with custom thresholds
    /// </summary>
    public static IServiceCollection ConfigurePerformanceThresholds(
        this IServiceCollection services, 
        Action<PerformanceMonitoringOptions> configure)
    {
        services.Configure(configure);
        return services;
    }
    
    /// <summary>
    /// Adds performance monitoring API endpoints
    /// </summary>
    public static IServiceCollection AddPerformanceMonitoringApi(this IServiceCollection services)
    {
        services.AddControllers();
        return services;
    }
}

/// <summary>
/// Performance monitoring configuration options
/// </summary>
public class PerformanceMonitoringOptions
{
    /// <summary>
    /// System resource monitoring settings
    /// </summary>
    public SystemResourceMonitoringOptions SystemResources { get; set; } = new();
    
    /// <summary>
    /// Database performance monitoring settings
    /// </summary>
    public DatabaseMonitoringOptions Database { get; set; } = new();
    
    /// <summary>
    /// WebSocket monitoring settings
    /// </summary>
    public WebSocketMonitoringOptions WebSocket { get; set; } = new();
    
    /// <summary>
    /// Trading pipeline monitoring settings
    /// </summary>
    public TradingPipelineMonitoringOptions TradingPipeline { get; set; } = new();
    
    /// <summary>
    /// Alerting configuration
    /// </summary>
    public AlertingOptions Alerting { get; set; } = new();
    
    /// <summary>
    /// Load testing configuration
    /// </summary>
    public LoadTestingOptions LoadTesting { get; set; } = new();
}

/// <summary>
/// System resource monitoring options
/// </summary>
public class SystemResourceMonitoringOptions
{
    /// <summary>
    /// Monitoring interval in seconds
    /// </summary>
    public int MonitoringIntervalSeconds { get; set; } = 5;
    
    /// <summary>
    /// CPU usage warning threshold (percentage)
    /// </summary>
    public double CpuWarningThreshold { get; set; } = 80.0;
    
    /// <summary>
    /// CPU usage critical threshold (percentage)
    /// </summary>
    public double CpuCriticalThreshold { get; set; } = 95.0;
    
    /// <summary>
    /// Memory usage warning threshold (percentage)
    /// </summary>
    public double MemoryWarningThreshold { get; set; } = 85.0;
    
    /// <summary>
    /// Memory usage critical threshold (percentage)
    /// </summary>
    public double MemoryCriticalThreshold { get; set; } = 95.0;
    
    /// <summary>
    /// Thread count warning threshold
    /// </summary>
    public int ThreadCountWarning { get; set; } = 100;
    
    /// <summary>
    /// Thread count critical threshold
    /// </summary>
    public int ThreadCountCritical { get; set; } = 200;
}

/// <summary>
/// Database monitoring options
/// </summary>
public class DatabaseMonitoringOptions
{
    /// <summary>
    /// Enable SQLite monitoring
    /// </summary>
    public bool EnableSqliteMonitoring { get; set; } = true;
    
    /// <summary>
    /// Enable Redis monitoring
    /// </summary>
    public bool EnableRedisMonitoring { get; set; } = true;
    
    /// <summary>
    /// Database query latency warning threshold (milliseconds)
    /// </summary>
    public double QueryLatencyWarningMs { get; set; } = 1000;
    
    /// <summary>
    /// Database query latency critical threshold (milliseconds)
    /// </summary>
    public double QueryLatencyCriticalMs { get; set; } = 5000;
    
    /// <summary>
    /// Redis operation latency warning threshold (milliseconds)
    /// </summary>
    public double RedisLatencyWarningMs { get; set; } = 100;
    
    /// <summary>
    /// Redis operation latency critical threshold (milliseconds)
    /// </summary>
    public double RedisLatencyCriticalMs { get; set; } = 500;
}

/// <summary>
/// WebSocket monitoring options
/// </summary>
public class WebSocketMonitoringOptions
{
    /// <summary>
    /// Enable WebSocket connection monitoring
    /// </summary>
    public bool EnableConnectionMonitoring { get; set; } = true;
    
    /// <summary>
    /// Enable message throughput monitoring
    /// </summary>
    public bool EnableThroughputMonitoring { get; set; } = true;
    
    /// <summary>
    /// Minimum connection ratio for alerts (0.0 to 1.0)
    /// </summary>
    public double MinConnectionRatio { get; set; } = 0.8;
    
    /// <summary>
    /// Maximum reconnections per hour before alert
    /// </summary>
    public int MaxReconnectionsPerHour { get; set; } = 10;
    
    /// <summary>
    /// Message processing latency warning threshold (milliseconds)
    /// </summary>
    public double MessageLatencyWarningMs { get; set; } = 100;
    
    /// <summary>
    /// Message processing latency critical threshold (milliseconds)
    /// </summary>
    public double MessageLatencyCriticalMs { get; set; } = 1000;
}

/// <summary>
/// Trading pipeline monitoring options
/// </summary>
public class TradingPipelineMonitoringOptions
{
    /// <summary>
    /// Enable end-to-end pipeline monitoring
    /// </summary>
    public bool EnablePipelineMonitoring { get; set; } = true;
    
    /// <summary>
    /// Enable bottleneck detection
    /// </summary>
    public bool EnableBottleneckDetection { get; set; } = true;
    
    /// <summary>
    /// Maximum active pipeline executions before alert
    /// </summary>
    public int MaxActiveExecutions { get; set; } = 50;
    
    /// <summary>
    /// Maximum bottlenecks per 5 minutes before alert
    /// </summary>
    public int MaxBottlenecksPer5Min { get; set; } = 10;
    
    /// <summary>
    /// Signal generation latency warning threshold (milliseconds)
    /// </summary>
    public double SignalLatencyWarningMs { get; set; } = 5000;
    
    /// <summary>
    /// Signal generation latency critical threshold (milliseconds)
    /// </summary>
    public double SignalLatencyCriticalMs { get; set; } = 15000;
    
    /// <summary>
    /// Trade execution latency warning threshold (milliseconds)
    /// </summary>
    public double TradeExecutionLatencyWarningMs { get; set; } = 2000;
    
    /// <summary>
    /// Trade execution latency critical threshold (milliseconds)
    /// </summary>
    public double TradeExecutionLatencyCriticalMs { get; set; } = 10000;
}

/// <summary>
/// Alerting options
/// </summary>
public class AlertingOptions
{
    /// <summary>
    /// Enable Discord notifications
    /// </summary>
    public bool EnableDiscordNotifications { get; set; } = true;
    
    /// <summary>
    /// Enable email notifications
    /// </summary>
    public bool EnableEmailNotifications { get; set; } = false;
    
    /// <summary>
    /// Cooldown period for critical alerts (minutes)
    /// </summary>
    public int CriticalAlertCooldownMinutes { get; set; } = 5;
    
    /// <summary>
    /// Cooldown period for warning alerts (minutes)
    /// </summary>
    public int WarningAlertCooldownMinutes { get; set; } = 15;
    
    /// <summary>
    /// Overall health score warning threshold (percentage)
    /// </summary>
    public double HealthScoreWarning { get; set; } = 70.0;
    
    /// <summary>
    /// Overall health score critical threshold (percentage)
    /// </summary>
    public double HealthScoreCritical { get; set; } = 50.0;
}

/// <summary>
/// Load testing options
/// </summary>
public class LoadTestingOptions
{
    /// <summary>
    /// Enable automatic load testing
    /// </summary>
    public bool EnableAutomaticTesting { get; set; } = false;
    
    /// <summary>
    /// Load test schedule (cron expression)
    /// </summary>
    public string TestSchedule { get; set; } = "0 2 * * 0"; // Weekly at 2 AM on Sunday
    
    /// <summary>
    /// Default test duration in minutes
    /// </summary>
    public int DefaultTestDurationMinutes { get; set; } = 5;
    
    /// <summary>
    /// Maximum concurrent operations for testing
    /// </summary>
    public int MaxConcurrentOperations { get; set; } = 50;
    
    /// <summary>
    /// Enable stress testing
    /// </summary>
    public bool EnableStressTesting { get; set; } = true;
}

using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using SmaTrendFollower.Services;
using System.Collections.Concurrent;

namespace SmaTrendFollower.Services;

/// <summary>
/// Performance alerting service with intelligent thresholds and Discord notifications
/// Monitors system health and sends alerts for performance degradation and bottlenecks
/// </summary>
public interface IPerformanceAlertingService : IDisposable
{
    /// <summary>
    /// Configures alert thresholds
    /// </summary>
    void ConfigureThresholds(PerformanceAlertThresholds thresholds);
    
    /// <summary>
    /// Manually triggers an alert
    /// </summary>
    Task TriggerAlertAsync(PerformanceAlert alert);
    
    /// <summary>
    /// Gets alert statistics
    /// </summary>
    Task<AlertStatistics> GetAlertStatisticsAsync();
    
    /// <summary>
    /// Starts the alerting service
    /// </summary>
    Task StartAsync(CancellationToken cancellationToken = default);
    
    /// <summary>
    /// Stops the alerting service
    /// </summary>
    Task StopAsync(CancellationToken cancellationToken = default);
}

/// <summary>
/// Performance alerting service implementation
/// </summary>
public sealed class PerformanceAlertingService : BackgroundService, IPerformanceAlertingService
{
    private readonly ILogger<PerformanceAlertingService> _logger;
    private readonly IDiscordNotificationService? _discordService;
    private readonly IRealTimePerformanceDashboard? _dashboard;
    private readonly ConcurrentDictionary<string, DateTime> _alertCooldowns;
    private readonly ConcurrentQueue<PerformanceAlert> _alertHistory;
    private PerformanceAlertThresholds _thresholds;
    private readonly Timer _monitoringTimer;
    private bool _disposed;

    public PerformanceAlertingService(
        ILogger<PerformanceAlertingService> logger,
        IDiscordNotificationService? discordService = null,
        IRealTimePerformanceDashboard? dashboard = null)
    {
        _logger = logger;
        _discordService = discordService;
        _dashboard = dashboard;
        _alertCooldowns = new ConcurrentDictionary<string, DateTime>();
        _alertHistory = new ConcurrentQueue<PerformanceAlert>();
        _thresholds = new PerformanceAlertThresholds();
        
        // Start monitoring timer
        _monitoringTimer = new Timer(CheckPerformanceMetrics, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
    }

    public void ConfigureThresholds(PerformanceAlertThresholds thresholds)
    {
        _thresholds = thresholds;
        _logger.LogInformation("Performance alert thresholds updated");
    }

    public async Task TriggerAlertAsync(PerformanceAlert alert)
    {
        var alertKey = $"{alert.AlertType}:{alert.Source}";
        
        // Check cooldown period
        if (_alertCooldowns.TryGetValue(alertKey, out var lastAlert))
        {
            var cooldownPeriod = GetCooldownPeriod(alert.Severity);
            if (DateTime.UtcNow - lastAlert < cooldownPeriod)
            {
                _logger.LogDebug("Alert {AlertKey} is in cooldown period", alertKey);
                return;
            }
        }
        
        // Record alert
        _alertHistory.Enqueue(alert);
        _alertCooldowns[alertKey] = DateTime.UtcNow;
        
        // Keep alert history manageable
        while (_alertHistory.Count > 1000)
        {
            _alertHistory.TryDequeue(out _);
        }
        
        // Send Discord notification
        if (_discordService != null)
        {
            await SendDiscordAlertAsync(alert);
        }
        
        _logger.LogWarning("Performance alert triggered: {AlertType} - {Message} (Severity: {Severity})",
            alert.AlertType, alert.Message, alert.Severity);
    }

    public async Task<AlertStatistics> GetAlertStatisticsAsync()
    {
        var now = DateTime.UtcNow;
        var last24Hours = now.AddHours(-24);
        var lastHour = now.AddHours(-1);
        
        var recentAlerts = _alertHistory.Where(a => a.Timestamp > last24Hours).ToList();
        var hourlyAlerts = _alertHistory.Where(a => a.Timestamp > lastHour).ToList();
        
        var stats = new AlertStatistics
        {
            Timestamp = now,
            TotalAlerts24Hours = recentAlerts.Count,
            TotalAlertsLastHour = hourlyAlerts.Count,
            AlertsByType = recentAlerts.GroupBy(a => a.AlertType).ToDictionary(g => g.Key, g => g.Count()),
            AlertsBySeverity = recentAlerts.GroupBy(a => a.Severity).ToDictionary(g => g.Key, g => g.Count()),
            MostFrequentAlerts = recentAlerts
                .GroupBy(a => $"{a.AlertType}:{a.Source}")
                .OrderByDescending(g => g.Count())
                .Take(5)
                .ToDictionary(g => g.Key, g => g.Count())
        };
        
        return await Task.FromResult(stats);
    }

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        _logger.LogInformation("Performance alerting service started");
        
        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                
                // Clean up old cooldowns
                CleanupCooldowns();
            }
            catch (OperationCanceledException)
            {
                break;
            }
        }
        
        _logger.LogInformation("Performance alerting service stopped");
    }

    private async void CheckPerformanceMetrics(object? state)
    {
        try
        {
            if (_dashboard == null) return;
            
            var dashboardData = await _dashboard.GetDashboardDataAsync();
            
            // Check overall health score
            if (dashboardData.OverallHealthScore < _thresholds.HealthScoreCritical)
            {
                await TriggerAlertAsync(new PerformanceAlert
                {
                    Timestamp = DateTime.UtcNow,
                    AlertType = "HealthScore",
                    Severity = "critical",
                    Source = "System",
                    Message = $"Overall health score is {dashboardData.OverallHealthScore:F1}% (critical threshold: {_thresholds.HealthScoreCritical}%)",
                    CurrentValue = dashboardData.OverallHealthScore,
                    Threshold = _thresholds.HealthScoreCritical
                });
            }
            else if (dashboardData.OverallHealthScore < _thresholds.HealthScoreWarning)
            {
                await TriggerAlertAsync(new PerformanceAlert
                {
                    Timestamp = DateTime.UtcNow,
                    AlertType = "HealthScore",
                    Severity = "warning",
                    Source = "System",
                    Message = $"Overall health score is {dashboardData.OverallHealthScore:F1}% (warning threshold: {_thresholds.HealthScoreWarning}%)",
                    CurrentValue = dashboardData.OverallHealthScore,
                    Threshold = _thresholds.HealthScoreWarning
                });
            }
            
            // Check system metrics
            if (dashboardData.SystemMetrics != null)
            {
                await CheckSystemMetrics(dashboardData.SystemMetrics);
            }
            
            // Check pipeline metrics
            if (dashboardData.PipelineMetrics != null)
            {
                await CheckPipelineMetrics(dashboardData.PipelineMetrics);
            }
            
            // Check WebSocket metrics
            if (dashboardData.WebSocketMetrics != null)
            {
                await CheckWebSocketMetrics(dashboardData.WebSocketMetrics);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to check performance metrics for alerting");
        }
    }

    private async Task CheckSystemMetrics(SystemResourceMetrics metrics)
    {
        // CPU usage alert
        if (metrics.CpuUsagePercent > _thresholds.CpuUsageCritical)
        {
            await TriggerAlertAsync(new PerformanceAlert
            {
                Timestamp = DateTime.UtcNow,
                AlertType = "CPU",
                Severity = "critical",
                Source = "System",
                Message = $"CPU usage is {metrics.CpuUsagePercent:F1}% (critical threshold: {_thresholds.CpuUsageCritical}%)",
                CurrentValue = metrics.CpuUsagePercent,
                Threshold = _thresholds.CpuUsageCritical
            });
        }
        
        // Memory usage alert
        var memoryUsagePercent = (metrics.MemoryUsageBytes / (double)(metrics.MemoryUsageBytes + metrics.MemoryAvailableBytes)) * 100;
        if (memoryUsagePercent > _thresholds.MemoryUsageCritical)
        {
            await TriggerAlertAsync(new PerformanceAlert
            {
                Timestamp = DateTime.UtcNow,
                AlertType = "Memory",
                Severity = "critical",
                Source = "System",
                Message = $"Memory usage is {memoryUsagePercent:F1}% (critical threshold: {_thresholds.MemoryUsageCritical}%)",
                CurrentValue = memoryUsagePercent,
                Threshold = _thresholds.MemoryUsageCritical
            });
        }
    }

    private async Task CheckPipelineMetrics(TradingPipelineMetrics metrics)
    {
        // Too many active executions
        if (metrics.ActiveExecutions > _thresholds.MaxActiveExecutions)
        {
            await TriggerAlertAsync(new PerformanceAlert
            {
                Timestamp = DateTime.UtcNow,
                AlertType = "Pipeline",
                Severity = "warning",
                Source = "TradingPipeline",
                Message = $"Too many active pipeline executions: {metrics.ActiveExecutions} (threshold: {_thresholds.MaxActiveExecutions})",
                CurrentValue = metrics.ActiveExecutions,
                Threshold = _thresholds.MaxActiveExecutions
            });
        }
        
        // Recent bottlenecks
        var recentBottlenecks = metrics.RecentBottlenecks.Count(b => b.Timestamp > DateTime.UtcNow.AddMinutes(-5));
        if (recentBottlenecks > _thresholds.MaxBottlenecksPer5Min)
        {
            await TriggerAlertAsync(new PerformanceAlert
            {
                Timestamp = DateTime.UtcNow,
                AlertType = "Bottleneck",
                Severity = "warning",
                Source = "TradingPipeline",
                Message = $"Too many recent bottlenecks: {recentBottlenecks} in last 5 minutes (threshold: {_thresholds.MaxBottlenecksPer5Min})",
                CurrentValue = recentBottlenecks,
                Threshold = _thresholds.MaxBottlenecksPer5Min
            });
        }
    }

    private async Task CheckWebSocketMetrics(WebSocketPerformanceMetrics metrics)
    {
        var totalConnections = metrics.ConnectionMetrics.Count;
        var activeConnections = metrics.ConnectionMetrics.Values.Count(c => c.State == System.Net.WebSockets.WebSocketState.Open);
        
        if (totalConnections > 0)
        {
            var connectionRatio = (double)activeConnections / totalConnections;
            if (connectionRatio < _thresholds.MinWebSocketConnectionRatio)
            {
                await TriggerAlertAsync(new PerformanceAlert
                {
                    Timestamp = DateTime.UtcNow,
                    AlertType = "WebSocket",
                    Severity = "warning",
                    Source = "WebSocketConnections",
                    Message = $"Low WebSocket connection ratio: {connectionRatio:P1} ({activeConnections}/{totalConnections})",
                    CurrentValue = connectionRatio * 100,
                    Threshold = _thresholds.MinWebSocketConnectionRatio * 100
                });
            }
        }
    }

    private async Task SendDiscordAlertAsync(PerformanceAlert alert)
    {
        try
        {
            var emoji = alert.Severity switch
            {
                "critical" => "🚨",
                "warning" => "⚠️",
                _ => "ℹ️"
            };
            
            var message = $"{emoji} **Performance Alert**\n" +
                         $"**Type:** {alert.AlertType}\n" +
                         $"**Severity:** {alert.Severity.ToUpper()}\n" +
                         $"**Source:** {alert.Source}\n" +
                         $"**Message:** {alert.Message}\n" +
                         $"**Time:** {alert.Timestamp:yyyy-MM-dd HH:mm:ss} UTC";
            
            if (_discordService != null)
            {
                await _discordService.SendMessageAsync(message);
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to send Discord alert notification");
        }
    }

    private TimeSpan GetCooldownPeriod(string severity)
    {
        return severity switch
        {
            "critical" => TimeSpan.FromMinutes(5),
            "warning" => TimeSpan.FromMinutes(15),
            _ => TimeSpan.FromMinutes(30)
        };
    }

    private void CleanupCooldowns()
    {
        var cutoff = DateTime.UtcNow.AddHours(-1);
        var expiredKeys = _alertCooldowns
            .Where(kvp => kvp.Value < cutoff)
            .Select(kvp => kvp.Key)
            .ToList();
        
        foreach (var key in expiredKeys)
        {
            _alertCooldowns.TryRemove(key, out _);
        }
    }

    public override void Dispose()
    {
        if (!_disposed)
        {
            _monitoringTimer?.Dispose();
            _disposed = true;
        }
        
        base.Dispose();
    }
}

/// <summary>
/// Performance alert thresholds configuration
/// </summary>
public class PerformanceAlertThresholds
{
    // System resource thresholds
    public double CpuUsageWarning { get; set; } = 80.0;
    public double CpuUsageCritical { get; set; } = 95.0;
    public double MemoryUsageWarning { get; set; } = 85.0;
    public double MemoryUsageCritical { get; set; } = 95.0;

    // Health score thresholds
    public double HealthScoreWarning { get; set; } = 70.0;
    public double HealthScoreCritical { get; set; } = 50.0;

    // Pipeline thresholds
    public int MaxActiveExecutions { get; set; } = 50;
    public int MaxBottlenecksPer5Min { get; set; } = 10;

    // WebSocket thresholds
    public double MinWebSocketConnectionRatio { get; set; } = 0.8; // 80% connections should be active

    // Database thresholds
    public double MaxDatabaseLatencyMs { get; set; } = 1000;
    public double MaxRedisLatencyMs { get; set; } = 100;

    // API thresholds
    public double MinApiRateLimitRemaining { get; set; } = 0.1; // 10% remaining
}

/// <summary>
/// Alert statistics model
/// </summary>
public class AlertStatistics
{
    public DateTime Timestamp { get; set; }
    public int TotalAlerts24Hours { get; set; }
    public int TotalAlertsLastHour { get; set; }
    public Dictionary<string, int> AlertsByType { get; set; } = new();
    public Dictionary<string, int> AlertsBySeverity { get; set; } = new();
    public Dictionary<string, int> MostFrequentAlerts { get; set; } = new();
}
